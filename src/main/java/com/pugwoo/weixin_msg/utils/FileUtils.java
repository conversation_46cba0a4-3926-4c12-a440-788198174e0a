package com.pugwoo.weixin_msg.utils;

import lombok.SneakyThrows;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardCopyOption;
import java.nio.file.attribute.FileTime;
import java.util.ArrayList;
import java.util.List;

public class FileUtils {

    public static void copy(String sourcePath, String destPath) throws IOException{
        Path source = Paths.get(sourcePath);
        Path destination = Paths.get(destPath);

        Files.createDirectories(destination.getParent());
        Files.copy(source, destination, StandardCopyOption.COPY_ATTRIBUTES, StandardCopyOption.REPLACE_EXISTING);
        FileTime lastModifiedTime = Files.getLastModifiedTime(source);
        Files.setLastModifiedTime(destination, lastModifiedTime);
    }

    public static void delete(String path) throws IOException {
        Files.deleteIfExists(Paths.get(path));
    }

    public static void move(String path1, String path2) throws IOException {
        Files.move(Paths.get(path1), Paths.get(path2), StandardCopyOption.REPLACE_EXISTING);
    }

    @SneakyThrows
    public static List<String> listFiles(String dir) {
        List<String> filePaths = new ArrayList<>();
        if (!FileUtils.isExist(dir)) {
            return filePaths;
        }
        Files.walk(Paths.get(dir))
                .filter(Files::isRegularFile)
                .forEach(path -> filePaths.add(path.toAbsolutePath().toString()));
        return filePaths;
    }

    public static boolean isExist(String absolutePath) {
        return Files.exists(Paths.get(absolutePath));
    }

    /**
     * 快速比较两个文件是否相同，允许1%以下的字节不同。
     * 该方法适合于比较微信的图片、视频等文件。
     *
     * 优化策略：
     * 1. 首先比较文件大小，如果差异超过1%则直接返回false
     * 2. 采用分段采样比较策略，不需要读取整个文件
     * 3. 优先比较文件头部和尾部，这些区域通常包含重要的元数据
     * 4. 对于大文件，采用跳跃式采样以提高速度
     *
     * @param sourcePath 源文件路径
     * @param destPath 目标文件路径
     * @return 如果文件相同（允许1%差异）返回true，否则返回false
     */
    public static boolean isSameFileQuickCheck(String sourcePath, String destPath) {
        File sourceFile = new File(sourcePath);
        File destFile = new File(destPath);

        // 检查文件是否存在
        if (!sourceFile.exists() || !destFile.exists()) {
            return false;
        }

        long sourceFileLength = sourceFile.length();
        long destFileLength = destFile.length();

        // 如果任一文件为空，则必须都为空才相同
        if (sourceFileLength == 0 || destFileLength == 0) {
            return sourceFileLength == destFileLength;
        }

        // 计算1%的差异阈值
        long maxFileLength = Math.max(sourceFileLength, destFileLength);
        long allowedDifference = maxFileLength / 100; // 1%

        // 如果文件大小差异超过1%，直接返回false
        if (Math.abs(sourceFileLength - destFileLength) > allowedDifference) {
            return false;
        }

        try (FileInputStream sourceStream = new FileInputStream(sourceFile);
             FileInputStream destStream = new FileInputStream(destFile)) {

            // 采用分段采样策略进行快速比较
            return performSampledComparison(sourceStream, destStream, sourceFileLength, destFileLength, allowedDifference);

        } catch (IOException e) {
            // 发生IO异常时认为文件不同
            return false;
        }
    }

    /**
     * 执行分段采样比较
     */
    private static boolean performSampledComparison(FileInputStream sourceStream, FileInputStream destStream,
                                                   long sourceLength, long destLength, long allowedDifference) throws IOException {

        // 确定采样策略
        int bufferSize = 8192; // 8KB缓冲区
        long minLength = Math.min(sourceLength, destLength);

        // 对于小文件（小于64KB），直接全量比较
        if (minLength <= 65536) {
            return compareFullContent(sourceStream, destStream, (int)minLength, allowedDifference);
        }

        // 对于大文件，采用采样比较策略
        return compareSampledContent(sourceStream, destStream, sourceLength, destLength, bufferSize, allowedDifference);
    }

    /**
     * 全量内容比较（用于小文件）
     */
    private static boolean compareFullContent(FileInputStream sourceStream, FileInputStream destStream,
                                            int length, long allowedDifference) throws IOException {
        byte[] sourceBuffer = new byte[length];
        byte[] destBuffer = new byte[length];

        int sourceRead = sourceStream.read(sourceBuffer);
        int destRead = destStream.read(destBuffer);

        if (sourceRead != destRead) {
            return Math.abs(sourceRead - destRead) <= allowedDifference;
        }

        // 计算不同字节数
        int differentBytes = 0;
        int compareLength = Math.min(sourceRead, destRead);

        for (int i = 0; i < compareLength; i++) {
            if (sourceBuffer[i] != destBuffer[i]) {
                differentBytes++;
            }
        }

        // 加上长度差异的字节数
        differentBytes += Math.abs(sourceRead - destRead);

        return differentBytes <= allowedDifference;
    }

    /**
     * 采样内容比较（用于大文件）
     */
    private static boolean compareSampledContent(FileInputStream sourceStream, FileInputStream destStream,
                                               long sourceLength, long destLength, int bufferSize, long allowedDifference) throws IOException {

        long minLength = Math.min(sourceLength, destLength);
        int differentBytes = 0;

        // 1. 比较文件头部（前16KB）
        differentBytes += compareSectionAtPosition(sourceStream, destStream, 0, Math.min(16384, (int)minLength));
        if (differentBytes > allowedDifference) {
            return false;
        }

        // 2. 比较文件尾部（后16KB）
        if (minLength > 32768) { // 如果文件大于32KB，才比较尾部
            long tailPosition = minLength - 16384;
            sourceStream.getChannel().position(tailPosition);
            destStream.getChannel().position(tailPosition);
            differentBytes += compareSectionAtPosition(sourceStream, destStream, tailPosition, 16384);
            if (differentBytes > allowedDifference) {
                return false;
            }
        }

        // 3. 采样比较中间部分（每隔一定距离采样一小段）
        if (minLength > 65536) { // 如果文件大于64KB，才进行中间采样
            int sampleCount = 5; // 采样5个点
            int sampleSize = 4096; // 每个采样点4KB
            long step = (minLength - 32768) / (sampleCount + 1); // 排除头尾32KB

            for (int i = 1; i <= sampleCount; i++) {
                long position = 16384 + step * i; // 从头部16KB后开始
                if (position + sampleSize <= minLength - 16384) { // 确保不超过尾部16KB
                    sourceStream.getChannel().position(position);
                    destStream.getChannel().position(position);
                    differentBytes += compareSectionAtPosition(sourceStream, destStream, position, sampleSize);
                    if (differentBytes > allowedDifference) {
                        return false;
                    }
                }
            }
        }

        // 加上文件长度差异
        differentBytes += Math.abs(sourceLength - destLength);

        return differentBytes <= allowedDifference;
    }

    /**
     * 比较指定位置的文件段
     */
    private static int compareSectionAtPosition(FileInputStream sourceStream, FileInputStream destStream,
                                              long position, int length) throws IOException {
        byte[] sourceBuffer = new byte[length];
        byte[] destBuffer = new byte[length];

        int sourceRead = sourceStream.read(sourceBuffer);
        int destRead = destStream.read(destBuffer);

        int differentBytes = 0;
        int compareLength = Math.min(sourceRead, destRead);

        for (int i = 0; i < compareLength; i++) {
            if (sourceBuffer[i] != destBuffer[i]) {
                differentBytes++;
            }
        }

        // 加上读取长度差异
        differentBytes += Math.abs(sourceRead - destRead);

        return differentBytes;
    }
}
